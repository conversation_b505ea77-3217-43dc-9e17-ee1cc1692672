# Ultra-Low Memory & Energy-Constrained Edge Computing for DICOM Image Aggregation

## Analysis Results and Answers

### Image Properties Analysis
- **Image Dimensions**: 512 × 512 pixels
- **Bit Depth**: 16 bits (2 bytes per pixel)
- **Number of Images**: 30 (as specified in requirements)

### Question 1: Memory Footprint of a Single Image
**What is the memory footprint of a single image in raw pixel data?**

**Answer**: 512.00 KB

**Calculation**:
- 512 rows × 512 columns = 262,144 pixels
- 262,144 pixels × 2 bytes/pixel = 524,288 bytes
- 524,288 bytes ÷ 1,024 = 512.00 KB

### Question 2: Total Memory for All Images
**What would be the total memory required if you were to load all 30 images simultaneously?**

**Answer**: 15,360.00 KB (15.00 MB)

**Calculation**:
- 512.00 KB per image × 30 images = 15,360.00 KB
- 15,360.00 KB ÷ 1,024 = 15.00 MB

### Question 3: Comparison to RAM Constraint
**Compare this to your 100 KB RAM constraint. What is the ratio of required memory to available memory?**

**Answer**: 153.6:1 ratio (required:available)

**Calculation**:
- Required memory: 15,360.00 KB
- Available memory: 100 KB
- Ratio: 15,360.00 KB ÷ 100 KB = 153.6:1

**Implications for Algorithm Design in a Battery-Powered Context**:
1. **Memory Constraint**: We cannot load all images simultaneously as it would exceed available memory by over 15,260%.
2. **Streaming Processing**: Must process images one at a time, and even then, we need to process each image in chunks.
3. **Memory Management**: Requires careful memory allocation, deallocation, and garbage collection.
4. **I/O Optimization**: Need to minimize disk I/O operations as they are energy-intensive and slow.
5. **Algorithm Selection**: Must use an online/streaming algorithm for standard deviation calculation that doesn't require storing all values.
6. **Precision Considerations**: May need to balance numerical precision with memory usage.
7. **Battery Impact**: Every unnecessary memory operation or disk read consumes precious battery power.

### Question 4: Rows That Fit in Available Memory
**Given the image dimensions, and assuming a 10 KB buffer for system/overhead, how many rows of the images can you fit into the remaining 90 KB of RAM?**

**Answer**: 90 rows (17.6% of the image)

**Calculation**:
- Available memory after overhead: 90 KB = 92,160 bytes
- Bytes per row: 512 columns × 2 bytes/pixel = 1,024 bytes per row
- Rows that fit: 92,160 bytes ÷ 1,024 bytes/row = 90 rows
- Percentage of total image: (90 ÷ 512) × 100% = 17.6%

## Algorithm Implementation Strategy

The implemented algorithm uses the following strategies to meet the strict memory and energy constraints:

1. **Welford's Online Algorithm**: Uses a numerically stable, one-pass algorithm to calculate standard deviation without storing all values.

2. **Chunked Processing**: Processes images in chunks of 90 rows at a time (the maximum that fits in available memory).

3. **Minimal Memory Footprint**:
   - Only keeps running accumulators (mean and M2) in memory
   - Processes one image at a time
   - Loads only the necessary chunks of each image

4. **I/O Optimization**:
   - Reads each DICOM file only once
   - Processes in sequential order to minimize seek operations

5. **Garbage Collection**: Explicitly frees memory after processing each chunk to prevent memory leaks.

6. **Precision Management**: Uses double-precision floating point (float64) for accumulators to maintain numerical stability while using minimal memory.

## Standard Deviation Results

The pixel-wise standard deviation calculation across all 30 DICOM images has been completed successfully with the following statistics:

- **Mean Standard Deviation**: 459.08
- **Standard Deviation of Standard Deviations**: 128.47
- **Minimum Standard Deviation**: 3.74
- **Maximum Standard Deviation**: 873.95

The complete standard deviation map has been saved to `pixel_stddev_map.npy` for further analysis.

## Conclusion

This implementation successfully calculates the pixel-wise standard deviation across 30 DICOM images while strictly adhering to the 100 KB memory constraint. The algorithm is optimized for minimal disk I/O and CPU usage, making it suitable for battery-powered edge devices in remote medical settings.

The analysis confirms that memory management is the primary challenge in this scenario, with the total memory requirement exceeding the available memory by a factor of 153.6. By using a streaming approach and processing the images in chunks, we've demonstrated that it's possible to perform complex statistical operations on medical images even with severe hardware constraints.
