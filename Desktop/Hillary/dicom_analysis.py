#!/usr/bin/env python3
"""
Ultra-Low Memory DICOM Image Analysis for Edge Computing
TinyAI Medical - Edge Device Pixel-wise Standard Deviation Calculator

This algorithm calculates pixel-wise standard deviation across DICOM images
while adhering to strict memory constraints (100 KB RAM limit).

Requirements: numpy, pydicom

Author: Ondess Hilary
Date: 21 July 2025
"""

import os
import numpy as np
import pydicom
from typing import <PERSON>ple, List
import gc
import sys

class MemoryConstrainedDICOMAnalyzer:
    def __init__(self, dcm_directory: str, max_memory_kb: int = 100):
        """
        Initialize the analyzer with memory constraints.
        
        Args:
            dcm_directory: Path to directory containing DICOM files
            max_memory_kb: Maximum memory allowed in KB (default 100 KB)
        """
        self.dcm_directory = dcm_directory
        self.max_memory_bytes = max_memory_kb * 1024
        self.system_overhead_kb = 10
        self.available_memory_bytes = (max_memory_kb - self.system_overhead_kb) * 1024
        
        # Image properties (to be determined)
        self.image_dimensions = None
        self.bit_depth = None
        self.num_images = 0
        self.bytes_per_pixel = None
        
    def analyze_image_properties(self) -> <PERSON><PERSON>[Tuple[int, int], int, int]:
        """
        Analyze the first DICOM file to determine image properties.
        
        Returns:
            Tuple of (dimensions, bit_depth, num_images)
        """
        dcm_files = [f for f in os.listdir(self.dcm_directory) if f.endswith('.dcm')]
        dcm_files.sort()
        self.num_images = len(dcm_files)
        
        if not dcm_files:
            raise ValueError("No DICOM files found in directory")
        
        # Load first image to get properties
        first_file = os.path.join(self.dcm_directory, dcm_files[0])
        ds = pydicom.dcmread(first_file)
        
        # Get image dimensions
        self.image_dimensions = (ds.Rows, ds.Columns)
        
        # Get bit depth
        self.bit_depth = ds.BitsAllocated
        self.bytes_per_pixel = self.bit_depth // 8
        
        print(f"Image Analysis Results:")
        print(f"- Image Dimensions: {self.image_dimensions[0]} x {self.image_dimensions[1]} pixels")
        print(f"- Bit Depth: {self.bit_depth} bits")
        print(f"- Bytes per Pixel: {self.bytes_per_pixel}")
        print(f"- Number of Images: {self.num_images}")
        
        return self.image_dimensions, self.bit_depth, self.num_images
    
    def calculate_memory_requirements(self) -> dict:
        """
        Calculate memory requirements for different scenarios.
        
        Returns:
            Dictionary with memory calculations
        """
        if not self.image_dimensions or not self.bytes_per_pixel:
            raise ValueError("Must analyze image properties first")
        
        rows, cols = self.image_dimensions
        pixels_per_image = rows * cols
        
        # Memory for single image (raw pixel data)
        single_image_bytes = pixels_per_image * self.bytes_per_pixel
        single_image_kb = single_image_bytes / 1024
        
        # Memory for all images
        all_images_bytes = single_image_bytes * self.num_images
        all_images_kb = all_images_bytes / 1024
        all_images_mb = all_images_kb / 1024
        
        # Ratio to available memory
        memory_ratio = all_images_kb / 100  # 100 KB constraint
        
        # How many rows can fit in available memory
        bytes_per_row = cols * self.bytes_per_pixel
        rows_that_fit = self.available_memory_bytes // bytes_per_row
        
        results = {
            'single_image_bytes': single_image_bytes,
            'single_image_kb': single_image_kb,
            'all_images_bytes': all_images_bytes,
            'all_images_kb': all_images_kb,
            'all_images_mb': all_images_mb,
            'memory_ratio': memory_ratio,
            'bytes_per_row': bytes_per_row,
            'rows_that_fit': rows_that_fit,
            'available_memory_kb': self.available_memory_bytes / 1024
        }
        
        return results
    
    def calculate_pixelwise_stddev_streaming(self) -> np.ndarray:
        """
        Calculate pixel-wise standard deviation using streaming algorithm
        to minimize memory usage and disk I/O.
        
        Uses Welford's online algorithm for numerical stability.
        """
        if not self.image_dimensions:
            raise ValueError("Must analyze image properties first")
        
        rows, cols = self.image_dimensions
        
        # Determine how many rows we can process at once
        memory_calc = self.calculate_memory_requirements()
        rows_per_chunk = min(memory_calc['rows_that_fit'], rows)
        
        print(f"\nProcessing Strategy:")
        print(f"- Processing {rows_per_chunk} rows at a time")
        print(f"- Total chunks needed: {(rows + rows_per_chunk - 1) // rows_per_chunk}")
        
        # Initialize accumulators for the entire image
        pixel_mean = np.zeros((rows, cols), dtype=np.float64)
        pixel_m2 = np.zeros((rows, cols), dtype=np.float64)
        
        # Get list of DICOM files
        dcm_files = [f for f in os.listdir(self.dcm_directory) if f.endswith('.dcm')]
        dcm_files.sort()
        
        # Process images one by one
        for img_idx, dcm_file in enumerate(dcm_files):
            if img_idx >= 30:  # Only process first 30 images as specified
                break
                
            file_path = os.path.join(self.dcm_directory, dcm_file)
            
            # Process image in chunks (row-wise)
            for chunk_start in range(0, rows, rows_per_chunk):
                chunk_end = min(chunk_start + rows_per_chunk, rows)
                
                # Load only the required chunk of the image
                ds = pydicom.dcmread(file_path)
                image_data = ds.pixel_array[chunk_start:chunk_end, :].astype(np.float64)
                
                # Update running statistics using Welford's algorithm
                n = img_idx + 1
                delta = image_data - pixel_mean[chunk_start:chunk_end, :]
                pixel_mean[chunk_start:chunk_end, :] += delta / n
                delta2 = image_data - pixel_mean[chunk_start:chunk_end, :]
                pixel_m2[chunk_start:chunk_end, :] += delta * delta2
                
                # Force garbage collection to free memory
                del image_data
                del ds
                gc.collect()
            
            print(f"Processed image {img_idx + 1}/30: {dcm_file}")
        
        # Calculate final standard deviation
        # Use n-1 for sample standard deviation
        n_images = min(30, len(dcm_files))
        pixel_variance = pixel_m2 / (n_images - 1) if n_images > 1 else pixel_m2
        pixel_stddev = np.sqrt(pixel_variance)
        
        return pixel_stddev
    
    def run_analysis(self) -> dict:
        """
        Run complete analysis and return results.
        """
        print("=" * 60)
        print("TinyAI Medical - DICOM Analysis for Edge Computing")
        print("=" * 60)
        
        # Step 1: Analyze image properties
        dimensions, bit_depth, num_images = self.analyze_image_properties()
        
        # Step 2: Calculate memory requirements
        memory_calc = self.calculate_memory_requirements()
        
        # Step 3: Calculate pixel-wise standard deviation
        print(f"\nCalculating pixel-wise standard deviation...")
        stddev_map = self.calculate_pixelwise_stddev_streaming()
        
        # Compile results
        results = {
            'image_dimensions': dimensions,
            'bit_depth': bit_depth,
            'num_images': num_images,
            'memory_calculations': memory_calc,
            'stddev_map': stddev_map,
            'stddev_stats': {
                'mean': np.mean(stddev_map),
                'std': np.std(stddev_map),
                'min': np.min(stddev_map),
                'max': np.max(stddev_map)
            }
        }
        
        return results

def print_formatted_answers(results: dict):
    """
    Print formatted answers to the specific questions.
    """
    print("\n" + "=" * 80)
    print("ANSWERS TO QUESTIONS")
    print("=" * 80)
    
    mem_calc = results['memory_calculations']
    dims = results['image_dimensions']
    
    print(f"\n1. MEMORY FOOTPRINT OF A SINGLE IMAGE (RAW PIXEL DATA):")
    print(f"   Calculation: {dims[0]} rows × {dims[1]} columns × {results['bit_depth']//8} bytes/pixel")
    print(f"   = {dims[0] * dims[1] * (results['bit_depth']//8):,} bytes")
    print(f"   = {mem_calc['single_image_kb']:.2f} KB")
    
    print(f"\n2. TOTAL MEMORY FOR ALL 30 IMAGES:")
    print(f"   Calculation: {mem_calc['single_image_kb']:.2f} KB × 30 images")
    print(f"   = {mem_calc['single_image_kb'] * 30:.2f} KB")
    print(f"   = {(mem_calc['single_image_kb'] * 30) / 1024:.2f} MB")
    
    print(f"\n3. COMPARISON TO 100 KB RAM CONSTRAINT:")
    print(f"   Required memory: {mem_calc['single_image_kb'] * 30:.2f} KB")
    print(f"   Available memory: 100 KB")
    print(f"   Ratio: {(mem_calc['single_image_kb'] * 30) / 100:.1f}:1")
    print(f"   ")
    print(f"   IMPLICATIONS FOR BATTERY-POWERED CONTEXT:")
    print(f"   - Cannot load all images simultaneously (exceeds memory by {((mem_calc['single_image_kb'] * 30) / 100 - 1) * 100:.0f}%)")
    print(f"   - Must use streaming/chunked processing approach")
    print(f"   - Requires careful memory management and garbage collection")
    print(f"   - Need to minimize disk I/O to preserve battery life")
    
    print(f"\n4. ROWS THAT FIT IN REMAINING 90 KB RAM:")
    print(f"   Available memory after 10 KB system overhead: 90 KB = {90 * 1024:,} bytes")
    print(f"   Bytes per row: {dims[1]} columns × {results['bit_depth']//8} bytes/pixel = {mem_calc['bytes_per_row']:,} bytes")
    print(f"   Rows that fit: {90 * 1024:,} bytes ÷ {mem_calc['bytes_per_row']:,} bytes/row = {mem_calc['rows_that_fit']} rows")
    print(f"   Percentage of image: {(mem_calc['rows_that_fit'] / dims[0]) * 100:.1f}%")

if __name__ == "__main__":
    # Initialize analyzer
    dcm_dir = "dcm docs"
    analyzer = MemoryConstrainedDICOMAnalyzer(dcm_dir, max_memory_kb=100)
    
    try:
        # Run analysis
        results = analyzer.run_analysis()
        
        # Print formatted answers
        print_formatted_answers(results)
        
        # Save standard deviation map
        np.save("pixel_stddev_map.npy", results['stddev_map'])
        print(f"\nPixel-wise standard deviation map saved to 'pixel_stddev_map.npy'")
        
        print(f"\nStandard Deviation Statistics:")
        stats = results['stddev_stats']
        print(f"- Mean: {stats['mean']:.2f}")
        print(f"- Std: {stats['std']:.2f}")
        print(f"- Min: {stats['min']:.2f}")
        print(f"- Max: {stats['max']:.2f}")
        
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
